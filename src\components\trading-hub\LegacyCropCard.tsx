'use client';

import { useRouter } from 'next/navigation';
import { useGlobalState } from '@/lib/store';
import { Card, CardContent } from '@/components/ui/card';

interface LegacyCropCardProps {
  product: any;
}

export default function LegacyCropCard({ product }: LegacyCropCardProps) {
  const router = useRouter();
  const gState = useGlobalState();

  const handleClick = () => {
    gState.tradingHub['productDetails'].set(product);
    router.push(`/trading-hub?id=${product.id}`);
  };

  return (
    <Card
      onClick={handleClick}
      className="flex w-full flex-1 cursor-pointer py-5 md:py-2"
    >
      <CardContent className="flex w-full flex-1 flex-col items-center gap-2 pb-0 md:flex-row">
        <img
          className="size-[180px] object-contain md:mr-[15px] lg:mr-[30px]"
          src={product?.image || '/assets/no-product.png'}
          alt=""
        />
        <div className="grid w-full gap-1 md:grid-cols-2 lg:flex lg:justify-between lg:space-x-5">
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Crop Name</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.name}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Trading Volume</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.tradingAppCrop?.trading_volume || '-'}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Last Trade Price</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {(product?.tradingAppCrop?.lastTradeTransaction &&
                '₱ ' + product?.tradingAppCrop?.lastTradeTransaction?.price) ||
                '-'}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Last Traded</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {(product?.tradingAppCrop?.lastTradeTransaction &&
                new Date(product?.tradingAppCrop?.lastTradeTransaction?.updated_at).toLocaleString()) ||
                '-'}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Yesterday Price</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {(product?.tradingAppCrop?.yesterdayLastTradeTransaction &&
                '₱' + product?.tradingAppCrop?.yesterdayLastTradeTransaction?.price) ||
                '-'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
