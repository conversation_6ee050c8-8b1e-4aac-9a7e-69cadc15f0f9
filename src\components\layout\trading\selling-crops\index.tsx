'use client';

import { useEffect } from 'react';
import { SellerTable } from './seller-table';
import { columns } from './seller-table/column';
import useTradingHub from '@/hooks/useTradingHub';
import { useSearchParams } from 'next/navigation';

export default function Seller() {
  const searchParams = useSearchParams();
  const cropId = searchParams.get('id');

  const [tradingHubState, tradingHubFunction] = useTradingHub();

  useEffect(() => {
    cropId && tradingHubFunction.getTradingViewAllById(cropId, 1);
  }, [cropId]);

  return (
    <div className="pt-4">
      <SellerTable columns={columns} data={tradingHubState.viewAllCropsById} metadata={''} />
    </div>
  );
}
