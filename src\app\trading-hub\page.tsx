'use client';

import { useEffect, useState } from 'react';
import { useGlobalState } from '@/lib/store';
import { useRouter, useSearchParams } from 'next/navigation';

import Navbar from '@/components/Navbar';
import TradingMenu from '@/components/TradingMenu';
import useTradingHub from '@/hooks/useTradingHub';

import Seller from '@/components/layout/trading/selling-crops';
import Buyer from '@/components/layout/trading/buying-crops';
import TradingHistory from '@/components/layout/trading/trading-history';

import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LuListFilter, LuChevronRight } from 'react-icons/lu';
import { Loader2 } from 'lucide-react';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { Value } from '@radix-ui/react-select';

export default function Trading() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const type = searchParams.get('type');

  const [tradingHubState, tradingHubFunction] = useTradingHub();
  const [viewAllCrops, setViewAllCrops] = useState<any>();
  const [viewCrops, setViewCrops] = useState<any>();

  const [showProduct, setShowProduct] = useState(false);

  console.log('THIS ALL CROPS: ', viewAllCrops);

  useEffect(() => {
    if (id) {
      setShowProduct(true);
      tradingHubFunction.getTradingViewCrop(id);
    } else {
      setShowProduct(false);
    }
  }, [id]);

  console.log('ID: ', id);
  console.log('TYPE: ', type);

  useEffect(() => {
    tradingHubFunction.getTradingViewAll();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      await setViewAllCrops(tradingHubState.viewAllCrops);
      await setViewCrops(tradingHubState.viewCrops);
    };

    fetchData();
  }, [tradingHubState]);

  const [query, setQuery] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  const handleSearch = (event) => {
    const value = event.target.value.toLowerCase();
    setQuery(value);
    const results = viewAllCrops.filter(
      (item) => item.name?.toLowerCase().includes(value) || item.keywords?.toLowerCase().includes(value),
    );
    setFilteredData(results);
  };

  useEffect(() => {
    if (gStateP['user_type']?.value === 99) {
      router.push('/group-buying/');
    }
  }, []);

  return (
    <main className="min-h-screen font-poppins">
      <Navbar />
      <div className="">
        <TradingMenu />
      </div>

      {viewAllCrops ? (
        <section className="container mx-auto max-w-7xl pt-6">
          {showProduct ? (
            <div className="flex flex-col font-poppins">
              <div className="flex flex-col lg:px-[32px]">
                <div className="flex justify-between">
                  <div className="font-dmSans text-[20px] font-bold tracking-tight md:text-[25px]">
                    Trading Crop Details
                  </div>
                  <Button className="bg-[#2B3674] text-white md:w-[150px]">
                    {type === '1' ? 'Selling Crop' : 'Buy Crop'}
                  </Button>
                </div>
                <div className="flex items-center gap-1 pb-4 text-[14px] font-medium text-gray-600">
                  <span
                    onClick={() => {
                      setShowProduct(false);
                      router.push('/trading-hub');
                    }}
                    className="cursor-pointer text-blue-600 hover:underline"
                  >
                    Back
                  </span>
                  <LuChevronRight className="text-[#7778789a]" />
                  View Details
                </div>
              </div>
              <div className="flex flex-col md:flex-row">
                <div className="flex items-center justify-center md:w-1/3">
                  <img
                    className="w-[150px] object-scale-down md:w-[215px]"
                    src={gState.tradingHub['productDetails'].image?.value || '/assets/no-product.png'}
                    alt=""
                  />
                </div>
                <div className="md:w-2/3 lg:px-[32px]">
                  <h1 className="flex items-center gap-2 text-[20px] font-semibold">
                    {gState.tradingHub['productDetails'].name?.value}
                  </h1>
                  <div className="my-2 h-px w-full bg-neutral-400/25"></div>

                  <div className="grid gap-x-4 md:grid-cols-2">
                    <div className="flex flex-col">
                      <div className="flex justify-between gap-x-3">
                        <div className="w-2/3 text-[12px] text-[#4D4D4D]">Bid Volume</div>
                        <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                          {(gState.tradingHub['productDetails'].tradingAppCrop?.value &&
                            gState.tradingHub['productDetails'].tradingAppCrop?.trading_volume.value) ||
                            '-'}
                        </div>
                      </div>

                      <div className="flex justify-between gap-x-3">
                        <div className="w-2/3 text-[12px] text-[#4D4D4D]">Last Traded Price</div>
                        <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                          {(gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.value &&
                            '₱ ' +
                              gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.price
                                ?.value) ||
                            '-'}
                        </div>
                      </div>

                      <div className="flex justify-between gap-x-3">
                        <div className="w-2/3 text-[12px] text-[#4D4D4D]">Last Traded Quantity</div>
                        <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                          {(gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.value &&
                            gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.quantity?.value +
                              ' kg') ||
                            '-'}
                        </div>
                      </div>

                      <div></div>
                    </div>

                    <div className="grid">
                      <div className="flex justify-between gap-x-3">
                        <div className="w-2/3 text-[12px] text-[#4D4D4D]">Highest offer Today</div>
                        <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                          {'₱ ' + viewCrops?.todaysHighestOffer || '-'}
                        </div>
                      </div>

                      <div className="flex justify-between gap-x-3">
                        <div className="w-2/3 text-[12px] text-[#4D4D4D]">Lowest offer Today</div>
                        <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                          {'₱ ' + viewCrops?.todaysLowestOffer || '-'}
                        </div>
                      </div>

                      {/* Hidden */}
                      <div className="invisible flex justify-between gap-x-3">
                        <div className="w-2/3 text-[12px] text-[#4D4D4D]">Change</div>
                        <div className="flex flex-1 text-[14px] font-medium text-[#00B207]">-</div>
                      </div>

                      <div className="invisible flex justify-between gap-x-3">
                        <div className="w-2/3 text-[12px] text-[#4D4D4D]">Percentage Change</div>
                        <div className="flex flex-1 text-[14px] font-medium text-[#00B207]">-</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-center pt-5 md:pt-0">
                <Tabs defaultValue="1" className="w-full px-0 lg:px-[32px]">
                  <TabsList className="flex h-[53px] gap-x-4">
                    <TabsTrigger
                      value="1"
                      onClick={() => {
                        router.push(`/trading-hub?type=${1}&id=${id}`);
                      }}
                      className={`text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A]`}
                    >
                      Selling Crops
                    </TabsTrigger>
                    <TabsTrigger
                      value="2"
                      onClick={() => {
                        router.push(`/trading-hub?type=${2}&id=${id}`);
                      }}
                      className={`text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A]`}
                    >
                      Buying Crops
                    </TabsTrigger>
                    <TabsTrigger
                      value="bidding"
                      className={`text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A]`}
                    >
                      Bidding History
                    </TabsTrigger>
                  </TabsList>

                  {/* Content */}
                  <TabsContent value="1" className="py-4">
                    <Seller />
                  </TabsContent>
                  <TabsContent value="2" className="py-4">
                    <Buyer />
                  </TabsContent>
                  <TabsContent value="bidding" className="py-4">
                    <TradingHistory />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex flex-col justify-between gap-3 md:flex-row">
                <div className="flex space-x-2">
                  <Button
                    disabled
                    variant="outline"
                    className="hidden items-center gap-2 rounded-md border border-[#444A6D]/50 p-3 font-inter text-sm text-[#444A6D] lg:px-5"
                  >
                    <LuListFilter size={18} />
                    <span className="hidden md:block">Filters</span>
                  </Button>
                  <Input
                    type="text"
                    value={query}
                    onChange={handleSearch}
                    placeholder="Search..."
                    className="md:w-[300px]"
                  />
                </div>
                <Select
                  onValueChange={(e) => {
                    router.push(`/trading-hub/trade?type=${e}`);
                  }}
                >
                  <SelectTrigger className="bg-[#2B3674] text-white md:w-[150px]">
                    <SelectValue placeholder="Trade a Crop" />
                  </SelectTrigger>
                  <SelectContent className="font-poppins">
                    <SelectItem value="2">Buying crop</SelectItem>
                    <SelectItem value="1">Selling crop</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <ScrollArea className="w-full whitespace-nowrap">
                {query.length > 0 ? (
                  <div className="">
                    {filteredData.length > 0 ? (
                      <div className="flex flex-col gap-y-7 px-1 py-7">
                        {filteredData.map((product, index) => (
                          <CropCard key={index} product={product} />
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-5 md:py-2">
                        <img className="w-[200px] object-contain" src="/assets/no-result.png" alt="" />
                        <p className="text-center text-lg text-gray-500 md:text-xl">No crops found</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col gap-y-7 px-1 py-7">
                    {viewAllCrops?.map((product, index) => <CropCard key={index} product={product} />)}
                  </div>
                )}
                <ScrollBar orientation="vertical" />
              </ScrollArea>
            </div>
          )}
        </section>
      ) : (
        <div className="min-h-screen bg-gray-50">
          <div className="flex min-h-[50vh] flex-col items-center justify-center gap-2 text-center">
            <Loader2 className="mx-auto size-6 animate-spin text-amber-500" />
            <p>Loading...</p>
          </div>
        </div>
      )}
    </main>
  );
}

const CropCard = ({ product }) => {
  const router = useRouter();
  const gState = useGlobalState();

  return (
    <Card
      onClick={() => {
        gState.tradingHub['productDetails'].set(product);
        router.push(`/trading-hub?id=${product.id}`);
      }}
      className="flex w-full flex-1 cursor-pointer py-5 md:py-2"
    >
      <CardContent className="flex w-full flex-1 flex-col items-center gap-2 pb-0 md:flex-row">
        <img
          className="size-[180px] object-contain md:mr-[15px] lg:mr-[30px]"
          src={product?.image || '/assets/no-product.png'}
          alt=""
        />
        <div className="grid w-full gap-1 md:grid-cols-2 lg:flex lg:justify-between lg:space-x-5">
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Crop Name</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.name}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Trading Volume</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.tradingAppCrop?.trading_volume || '-'}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Last Trade Price</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {(product?.tradingAppCrop?.lastTradeTransaction &&
                '₱ ' + product?.tradingAppCrop?.lastTradeTransaction?.price) ||
                '-'}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Last Traded</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {(product?.tradingAppCrop?.lastTradeTransaction &&
                new Date(product?.tradingAppCrop?.lastTradeTransaction?.updated_at).toLocaleString()) ||
                '-'}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Yesterday Price</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {(product?.tradingAppCrop?.yesterdayLastTradeTransaction &&
                '₱' + product?.tradingAppCrop?.yesterdayLastTradeTransaction?.price) ||
                '-'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
