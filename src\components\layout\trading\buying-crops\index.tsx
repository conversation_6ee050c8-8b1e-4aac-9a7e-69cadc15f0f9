'use client';

import { useEffect } from 'react';
import { BuyerTable } from './buyer-table';
import { columns } from './buyer-table/column';
import useTradingHub from '@/hooks/useTradingHub';
import { useSearchParams } from 'next/navigation';

export default function Buyer() {
  const searchParams = useSearchParams();
  const cropId = searchParams.get('id');

  const [tradingHubState, tradingHubFunction] = useTradingHub();

  useEffect(() => {
    cropId && tradingHubFunction.getTradingViewAllById(cropId, 2);
  }, [cropId]);

  return (
    <div className="pt-4">
      <BuyerTable columns={columns} data={tradingHubState.viewAllCropsById} metadata={''} />
    </div>
  );
}
