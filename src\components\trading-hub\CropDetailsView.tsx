'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LuChevronRight } from 'react-icons/lu';
import { useGlobalState } from '@/lib/store';

import Seller from '@/components/layout/trading/selling-crops';
import Buyer from '@/components/layout/trading/buying-crops';
import TradingHistory from '@/components/layout/trading/trading-history';

interface CropDetailsViewProps {
  viewCrops: any;
}

export default function CropDetailsView({ viewCrops }: CropDetailsViewProps) {
  const router = useRouter();
  const gState = useGlobalState();
  const searchParams = useSearchParams();
  const type = searchParams.get('type');
  const id = searchParams.get('id');

  const handleBackClick = () => {
    router.push('/trading-hub');
  };

  return (
    <div className="flex flex-col font-poppins">
      <div className="flex flex-col lg:px-[32px]">
        <div className="flex justify-between">
          <div className="font-dmSans text-[20px] font-bold tracking-tight md:text-[25px]">Trading Crop Details</div>
          <Button className="bg-[#2B3674] text-white md:w-[150px]">{type === '1' ? 'Selling Crop' : 'Buy Crop'}</Button>
        </div>
        <div className="flex items-center gap-1 pb-4 text-[14px] font-medium text-gray-600">
          <span onClick={handleBackClick} className="cursor-pointer text-blue-600 hover:underline">
            Back
          </span>
          <LuChevronRight className="text-[#7778789a]" />
          View Details
        </div>
      </div>

      <div className="flex flex-col md:flex-row">
        <div className="flex items-center justify-center md:w-1/3">
          <img
            className="w-[150px] object-scale-down md:w-[215px]"
            src={gState.tradingHub['productDetails'].image?.value || '/assets/no-product.png'}
            alt=""
          />
        </div>
        <div className="md:w-2/3 lg:px-[32px]">
          <h1 className="flex items-center gap-2 text-[20px] font-semibold">
            {gState.tradingHub['productDetails'].name?.value}
          </h1>
          <div className="my-2 h-px w-full bg-neutral-400/25"></div>

          <div className="grid gap-x-4 md:grid-cols-2">
            <div className="flex flex-col">
              <div className="flex justify-between gap-x-3">
                <div className="w-2/3 text-[12px] text-[#4D4D4D]">Bid Volume</div>
                <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                  {(gState.tradingHub['productDetails'].tradingAppCrop?.value &&
                    gState.tradingHub['productDetails'].tradingAppCrop?.trading_volume.value) ||
                    '-'}
                </div>
              </div>

              <div className="flex justify-between gap-x-3">
                <div className="w-2/3 text-[12px] text-[#4D4D4D]">Last Traded Price</div>
                <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                  {(gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.value &&
                    '₱ ' + gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.price?.value) ||
                    '-'}
                </div>
              </div>

              <div className="flex justify-between gap-x-3">
                <div className="w-2/3 text-[12px] text-[#4D4D4D]">Last Traded Quantity</div>
                <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                  {(gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.value &&
                    gState.tradingHub['productDetails']?.tradingAppCrop?.lastTradeTransaction?.quantity?.value +
                      ' kg') ||
                    '-'}
                </div>
              </div>

              <div></div>
            </div>

            <div className="grid">
              <div className="flex justify-between gap-x-3">
                <div className="w-2/3 text-[12px] text-[#4D4D4D]">Highest offer Today</div>
                <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                  {'₱ ' + viewCrops?.todaysHighestOffer || '-'}
                </div>
              </div>

              <div className="flex justify-between gap-x-3">
                <div className="w-2/3 text-[12px] text-[#4D4D4D]">Lowest offer Today</div>
                <div className="flex flex-1 text-[14px] font-medium text-[#2B3674]">
                  {'₱ ' + viewCrops?.todaysLowestOffer || '-'}
                </div>
              </div>

              {/* Hidden */}
              <div className="invisible flex justify-between gap-x-3">
                <div className="w-2/3 text-[12px] text-[#4D4D4D]">Change</div>
                <div className="flex flex-1 text-[14px] font-medium text-[#00B207]">-</div>
              </div>

              <div className="invisible flex justify-between gap-x-3">
                <div className="w-2/3 text-[12px] text-[#4D4D4D]">Percentage Change</div>
                <div className="flex flex-1 text-[14px] font-medium text-[#00B207]">-</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-center pt-5 md:pt-0">
        <Tabs defaultValue="1" className="w-full px-0 lg:px-[32px]">
          <TabsList className="flex h-[53px] gap-x-4">
            <TabsTrigger
              value="1"
              onClick={() => {
                router.push(`/trading-hub?type=${1}&id=${id}`);
              }}
              className={`text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A]`}
            >
              Selling Crops
            </TabsTrigger>
            <TabsTrigger
              value="2"
              onClick={() => {
                router.push(`/trading-hub?type=${2}&id=${id}`);
              }}
              className={`text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A]`}
            >
              Buying Crops
            </TabsTrigger>
            <TabsTrigger
              value="bidding"
              className={`text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A]`}
            >
              Bidding History
            </TabsTrigger>
          </TabsList>

          {/* Content */}
          <TabsContent value="1" className="py-4">
            <Seller />
          </TabsContent>
          <TabsContent value="2" className="py-4">
            <Buyer />
          </TabsContent>
          <TabsContent value="bidding" className="py-4">
            <TradingHistory />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
